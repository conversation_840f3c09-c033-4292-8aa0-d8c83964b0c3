<?php
/**
 * Core Configuration
 * FTP-Based Portal with simplified authentication
 */

// Start session securely
if (session_status() == PHP_SESSION_NONE) {
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', 1);
    ini_set('session.use_only_cookies', 1);
    session_start();
}

// Core settings
define('APP_NAME', 'FTP Portal');
define('APP_VERSION', '2.0.0');
define('BASE_URL', 'http://localhost/portal-dev/');
define('CSRF_TOKEN_NAME', 'csrf_token');
define('FTP_DEMO_MODE', true);

// FTP Configuration
define('FTP_HOST', 'localhost');
define('FTP_PORT', 21);
define('FTP_BASE_PATH', '/invoices/');
define('FTP_TIMEOUT', 30);

// Available modules
define('AVAILABLE_MODULES', [
    'invoices' => [
        'name' => 'Invoices',
        'description' => 'View and manage invoice documents',
        'icon' => '📄'
    ],
    'freight' => [
        'name' => 'Freight Management',
        'description' => 'Freight forwarding and logistics',
        'icon' => '🚢'
    ],
    'tariff' => [
        'name' => 'Tariff Book',
        'description' => 'Customs tariff lookup',
        'icon' => '📚'
    ]
]);

// Security functions
function generateCSRFToken() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

function verifyCSRFToken($token) {
    return isset($_SESSION[CSRF_TOKEN_NAME]) && hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}

function sanitizeInput($data) {
    return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
}

function isLoggedIn() {
    return isset($_SESSION['user_email']) && isset($_SESSION['last_activity']) &&
           (time() - $_SESSION['last_activity'] < 3600);
}

function requireLogin() {
    if (!isLoggedIn()) {
        // Determine the correct path to login.php based on current directory
        $login_path = 'login.php';

        // If we're in a subdirectory (like modules/), go up one level
        if (strpos($_SERVER['REQUEST_URI'], '/modules/') !== false) {
            $login_path = '../login.php';
        }

        header('Location: ' . $login_path);
        exit();
    }
    $_SESSION['last_activity'] = time();
}

function authenticateUser($email, $password) {
    $email = sanitizeEmail($email);
    if (empty($email) || empty($password)) return false;

    // Demo credentials for testing
    $demo_users = [
        '<EMAIL>' => ['password' => 'admin123', 'role' => 'admin', 'name' => 'Administrator'],
        '<EMAIL>' => ['password' => 'master123', 'role' => 'masteradmin', 'name' => 'Master Administrator'],
        '<EMAIL>' => ['password' => 'demo123', 'role' => 'user', 'name' => 'Demo User'],
        '<EMAIL>' => ['password' => 'user123', 'role' => 'user', 'name' => 'Regular User']
    ];

    if (isset($demo_users[$email]) && $demo_users[$email]['password'] === $password) {
        $_SESSION['user_email'] = $email;
        $_SESSION['user_name'] = $demo_users[$email]['name'];
        $_SESSION['user_role'] = $demo_users[$email]['role'];
        $_SESSION['last_activity'] = time();

        // Set admin flags based on role
        $_SESSION['is_admin'] = ($demo_users[$email]['role'] === 'admin');
        $_SESSION['is_master_admin'] = ($demo_users[$email]['role'] === 'masteradmin');

        return true;
    }
    return false;
}

function sanitizeEmail($email) {
    $email = trim(strtolower($email));
    return filter_var($email, FILTER_VALIDATE_EMAIL) ? $email : '';
}

function sanitizeFTPUsername($username) {
    return strtolower(substr(trim(preg_replace('/[^a-zA-Z0-9_-]/', '', $username)), 0, 50));
}

// Admin role functions
function isAdmin() {
    return isLoggedIn() && isset($_SESSION['is_admin']) && $_SESSION['is_admin'] === true;
}

function isMasterAdmin() {
    return isLoggedIn() && isset($_SESSION['is_master_admin']) && $_SESSION['is_master_admin'] === true;
}

function hasAdminPrivileges() {
    return isAdmin() || isMasterAdmin();
}

function getAccessibleModules($email = null) {
    if ($email === null && !isLoggedIn()) return [];

    if ($email === null) {
        $email = $_SESSION['user_email'];
    }

    // Demo module access based on email
    $user_modules = [
        '<EMAIL>' => ['invoices', 'freight'],
        '<EMAIL>' => ['invoices', 'tariff'],
        '<EMAIL>' => array_keys(AVAILABLE_MODULES),
        '<EMAIL>' => array_keys(AVAILABLE_MODULES)
    ];

    if (!isset($user_modules[$email])) return [];

    $accessible = [];
    foreach ($user_modules[$email] as $module_key) {
        if (isset(AVAILABLE_MODULES[$module_key])) {
            $accessible[$module_key] = AVAILABLE_MODULES[$module_key];
        }
    }

    return $accessible;
}

// Security logging function
function logSecurityEvent($event_type, $details = '') {
    $log_entry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'event_type' => $event_type,
        'details' => $details,
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'session_id' => session_id()
    ];

    $log_message = sprintf(
        "[%s] %s - %s | IP: %s | Session: %s",
        $log_entry['timestamp'],
        $log_entry['event_type'],
        $log_entry['details'],
        $log_entry['ip_address'],
        substr($log_entry['session_id'], 0, 8)
    );

    // Log to PHP error log
    error_log("SECURITY_EVENT: " . $log_message);

    // Optionally log to a separate security log file
    $log_file = __DIR__ . '/../logs/security.log';
    $log_dir = dirname($log_file);

    // Create logs directory if it doesn't exist
    if (!is_dir($log_dir)) {
        @mkdir($log_dir, 0755, true);
    }

    // Write to security log file
    if (is_dir($log_dir) && is_writable($log_dir)) {
        file_put_contents($log_file, $log_message . PHP_EOL, FILE_APPEND | LOCK_EX);
    }
}

// FTP helper functions
function getUserFTPPath($username = null) {
    if ($username === null && !isLoggedIn()) {
        return false;
    }

    if ($username === null) {
        $username = $_SESSION['ftp_username'];
    }

    return FTP_BASE_PATH . sanitizeFTPUsername($username) . '/';
}

function getFTPConnection() {
    if (defined('FTP_DEMO_MODE') && FTP_DEMO_MODE) {
        return false; // Demo mode doesn't use real FTP
    }

    $connection = ftp_connect(FTP_HOST, FTP_PORT, FTP_TIMEOUT);
    if (!$connection) {
        return false;
    }

    // In real mode, you would use actual FTP credentials
    // For now, return false to force demo mode
    ftp_close($connection);
    return false;
}

function getDemoFiles($email) {
    $demo_files = [
        '<EMAIL>' => [
            [
                'filename' => 'invoice_demo_001.pdf',
                'invoice_number' => 'INV-DEMO-001',
                'amount' => 1250.00,
                'date' => '2024-01-15',
                'due_date' => '2024-02-15',
                'status' => 'pending',
                'description' => 'Professional Services - January 2024'
            ],
            [
                'filename' => 'invoice_demo_002.pdf',
                'invoice_number' => 'INV-DEMO-002',
                'amount' => 2750.50,
                'date' => '2024-01-20',
                'due_date' => '2024-02-20',
                'status' => 'overdue',
                'description' => 'Freight Services - Shipment #FS-001'
            ]
        ],
        '<EMAIL>' => [
            [
                'filename' => 'invoice_test_001.pdf',
                'invoice_number' => 'INV-TEST-001',
                'amount' => 890.25,
                'date' => '2024-01-25',
                'due_date' => '2024-02-25',
                'status' => 'paid',
                'description' => 'Customs Clearance Services'
            ]
        ]
    ];

    return $demo_files[$email] ?? [];
}

function secureFileDownload($filename) {
    if (!isLoggedIn()) {
        return false;
    }

    // Sanitize filename
    $filename = sanitizeInput($filename);
    $filename = basename($filename); // Prevent directory traversal

    // Validate file extension
    if (!preg_match('/\.pdf$/i', $filename)) {
        logSecurityEvent('INVALID_FILE_TYPE', 'Attempted download: ' . $filename);
        return false;
    }

    // In demo mode, simulate file download
    if (defined('FTP_DEMO_MODE') && FTP_DEMO_MODE) {
        // Log successful download
        logSecurityEvent('FILE_DOWNLOAD', 'Demo file: ' . $filename);

        // Generate demo PDF content
        $pdf_content = generateDemoPDF($filename);

        // Set headers for PDF download
        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Length: ' . strlen($pdf_content));
        header('Cache-Control: private, max-age=0, must-revalidate');
        header('Pragma: public');

        echo $pdf_content;
        return true;
    }

    // In real FTP mode, download from FTP server
    // This would implement actual FTP file download
    return false;
}

function generateDemoPDF($filename) {
    // Simple PDF content for demo purposes
    $pdf_content = "%PDF-1.4\n";
    $pdf_content .= "1 0 obj\n<< /Type /Catalog /Pages 2 0 R >>\nendobj\n";
    $pdf_content .= "2 0 obj\n<< /Type /Pages /Kids [3 0 R] /Count 1 >>\nendobj\n";
    $pdf_content .= "3 0 obj\n<< /Type /Page /Parent 2 0 R /MediaBox [0 0 612 792] /Contents 4 0 R >>\nendobj\n";
    $pdf_content .= "4 0 obj\n<< /Length 44 >>\nstream\nBT\n/F1 12 Tf\n100 700 Td\n(Demo Invoice: " . $filename . ") Tj\nET\nendstream\nendobj\n";
    $pdf_content .= "xref\n0 5\n0000000000 65535 f \n0000000009 00000 n \n0000000058 00000 n \n0000000115 00000 n \n0000000206 00000 n \n";
    $pdf_content .= "trailer\n<< /Size 5 /Root 1 0 R >>\nstartxref\n300\n%%EOF";

    return $pdf_content;
}

function checkDemoPaymentStatus($filename, $email) {
    // Demo payment status - some files are marked as paid
    $paid_files = [
        '<EMAIL>' => ['invoice_demo_002.pdf'],
        '<EMAIL>' => ['invoice_test_001.pdf']
    ];

    return in_array($filename, $paid_files[$email] ?? []);
}
?>
