<?php
/**
 * FTP-Based Dashboard Page
 * Main dashboard showing available modules for FTP users
 */

require_once 'config/config.php';

// Require login
requireLogin();

// Redirect admin users to admin dashboard
if (isAdmin()) {
    header('Location: admin_dashboard.php');
    exit();
}

// Get user's accessible modules based on permissions
$accessible_modules = getAccessibleModules();
$user_rights = array_keys($accessible_modules);

// Define module information
$modules = [
    'invoices' => [
        'title' => 'Commercial Invoices',
        'description' => 'Manage commercial invoices, customs declarations, and payment processing for international trade transactions',
        'icon' => 'invoices',
        'url' => 'modules/invoices.php',
        'color' => '#3b82f6',
        'actions' => [
            ['label' => 'View Invoices', 'icon' => '📄', 'action' => 'viewInvoices'],
            ['label' => 'Pay Duties', 'icon' => '💳', 'action' => 'payOutstanding'],
            ['label' => 'Export Docs', 'icon' => '📥', 'action' => 'downloadPDFs']
        ]
    ],
    'freight' => [
        'title' => 'Freight Management',
        'description' => 'Advanced freight forwarding, container tracking, and logistics coordination for efficient cargo management',
        'icon' => 'freight',
        'url' => 'modules/freight.php',
        'color' => '#10b981',
        'actions' => [
            ['label' => 'Container Tracking', 'icon' => '📦', 'action' => 'trackContainers'],
            ['label' => 'Shipment Planning', 'icon' => '🗺️', 'action' => 'planShipments'],
            ['label' => 'Logistics Reports', 'icon' => '📊', 'action' => 'viewReports']
        ]
    ],
    'tariff' => [
        'title' => 'Customs Tariff',
        'description' => 'Access HS codes, duty calculations, and customs tariff information for accurate import/export compliance',
        'icon' => 'tariff',
        'url' => 'modules/tariff.php',
        'color' => '#f59e0b',
        'actions' => [
            ['label' => 'Calculate Duty', 'icon' => '🧮', 'action' => 'calculateDuty'],
            ['label' => 'HS Code Search', 'icon' => '🔍', 'action' => 'searchCodes'],
            ['label' => 'Tariff Rates', 'icon' => '📖', 'action' => 'rateLookup']
        ]
    ],
    'accounting' => [
        'title' => 'Trade Finance',
        'description' => 'Financial management, trade finance, and accounting operations for international commerce activities',
        'icon' => 'accounting',
        'url' => 'modules/accounting.php',
        'color' => '#8b5cf6',
        'actions' => [
            ['label' => 'Trade Statements', 'icon' => '📋', 'action' => 'viewStatements'],
            ['label' => 'Financial Reports', 'icon' => '📈', 'action' => 'generateReport'],
            ['label' => 'Export Data', 'icon' => '💾', 'action' => 'exportData']
        ]
    ],
    'backoffice' => [
        'title' => 'Official Website',
        'description' => 'Access the main company website and official resources for additional services and information',
        'icon' => 'backoffice',
        'url' => 'https://your-company-website.com',
        'color' => '#ef4444',
        'external' => true,
        'actions' => [
            ['label' => 'Visit Website', 'icon' => '🌐', 'action' => 'openWebsite'],
            ['label' => 'Contact Support', 'icon' => '📞', 'action' => 'contactSupport'],
            ['label' => 'Company Info', 'icon' => 'ℹ️', 'action' => 'companyInfo']
        ]
    ]
];

// Use accessible modules based on user permissions
$available_modules = [];
foreach ($accessible_modules as $key => $module_info) {
    // Convert new module structure to old format for compatibility
    $available_modules[$key] = [
        'title' => $module_info['name'],
        'description' => $module_info['description'],
        'icon' => strtolower(str_replace(' ', '', $key)),
        'url' => $key === 'backoffice' ? 'https://your-company-website.com' : 'modules/' . $key . '.php',
        'color' => getModuleColor($key),
        'actions' => isset($modules[$key]['actions']) ? $modules[$key]['actions'] : []
    ];
}

// Add message for users with no module access
$has_no_access = empty($available_modules);

/**
 * Get module color based on module key
 */
function getModuleColor($key) {
    $colors = [
        'invoices' => '#3b82f6',
        'freight' => '#10b981',
        'tariff' => '#f59e0b',
        'accounting' => '#8b5cf6',
        'backoffice' => '#ef4444'
    ];
    return isset($colors[$key]) ? $colors[$key] : '#6b7280';
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body class="dashboard">
    <header class="header">
        <div class="header-content">
            <div class="header-left">
                <div class="brand-section">
                    <div class="brand-logo">
                        <img src="assets/img/logo.png" alt="<?php echo APP_NAME; ?>" class="logo-image">
                    </div>
                    <div class="brand-text">
                        <span class="brand-subtitle">Customs Clearing Portal</span>
                    </div>
                </div>
                <nav class="main-navigation">
                    <a href="dashboard.php" class="nav-item active">
                        Dashboard
                    </a>

                    <?php if (!empty($available_modules)): ?>
                    <div class="modules-dropdown-container">
                        <button class="nav-item dropdown-toggle" onclick="toggleModulesDropdown()">
                            Modules
                            <span class="dropdown-arrow">▼</span>
                        </button>

                        <div class="modules-dropdown" id="modulesDropdown">
                            <div class="modules-dropdown-header">
                                <h4>Available Modules</h4>
                            </div>
                            <div class="modules-dropdown-list">
                                <?php foreach ($available_modules as $key => $module): ?>
                                <a href="<?php echo $module['url']; ?>" class="module-dropdown-item">
                                    <div class="module-dropdown-content">
                                        <div class="module-dropdown-title"><?php echo htmlspecialchars($module['title']); ?></div>
                                        <div class="module-dropdown-desc"><?php echo htmlspecialchars($module['description']); ?></div>
                                    </div>
                                </a>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </nav>
            </div>

            <div class="header-right">
                <div class="professional-actions">
                    <!-- Main System Link -->
                    <a href="https://acs.clearight.co.za:8080/" target="_blank" class="professional-link" title="Visit Main System">
                        Main System
                    </a>

                    <!-- Notifications -->
                    <div class="notification-container">
                        <button class="professional-btn notification-btn" title="Notifications" onclick="toggleNotifications()">
                            <svg width="16" height="16" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"/>
                            </svg>
                            <span class="notification-badge">3</span>
                        </button>

                        <!-- Notifications Dropdown -->
                        <div class="notifications-dropdown" id="notificationsDropdown">
                            <div class="notifications-header">
                                <h4>Notifications</h4>
                                <button class="mark-all-read" onclick="markAllRead()">Mark all read</button>
                            </div>
                            <div class="notifications-list">
                                <div class="notification-item unread">
                                    <div class="notification-icon">📄</div>
                                    <div class="notification-content">
                                        <div class="notification-title">New Invoice Available</div>
                                        <div class="notification-text">Invoice INV-2024-004 has been uploaded</div>
                                        <div class="notification-time">2 hours ago</div>
                                    </div>
                                </div>
                                <div class="notification-item unread">
                                    <div class="notification-icon">💳</div>
                                    <div class="notification-content">
                                        <div class="notification-title">Payment Reminder</div>
                                        <div class="notification-text">Invoice INV-2024-002 is overdue</div>
                                        <div class="notification-time">1 day ago</div>
                                    </div>
                                </div>
                                <div class="notification-item unread">
                                    <div class="notification-icon">✅</div>
                                    <div class="notification-content">
                                        <div class="notification-title">Payment Confirmed</div>
                                        <div class="notification-text">Payment for INV-2024-001 processed successfully</div>
                                        <div class="notification-time">3 days ago</div>
                                    </div>
                                </div>
                            </div>
                            <div class="notifications-footer">
                                <a href="modules/notifications.php" class="view-all-link">View All Notifications</a>
                            </div>
                        </div>
                    </div>

                    <!-- User Menu -->
                    <div class="user-menu-container">
                        <button class="user-menu-trigger" onclick="toggleSettings()">
                            <div class="user-avatar-small">
                                <span><?php echo strtoupper(substr($_SESSION['user_name'], 0, 2)); ?></span>
                            </div>
                            <div class="user-info-compact">
                                <div class="user-name-compact"><?php echo htmlspecialchars($_SESSION['user_name']); ?></div>
                                <div class="user-role-compact"><?php echo ucfirst($_SESSION['user_role']); ?></div>
                            </div>
                            <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor" class="dropdown-chevron">
                                <path d="M3 4.5L6 7.5L9 4.5"/>
                            </svg>
                        </button>

                        <!-- User Menu Dropdown -->
                        <div class="user-menu-dropdown" id="settingsDropdown">
                            <div class="user-menu-header">
                                <div class="user-profile-info">
                                    <div class="user-name-full"><?php echo htmlspecialchars($_SESSION['user_name']); ?></div>
                                    <div class="user-details-full">
                                        <span class="client-name">Email: <?php echo htmlspecialchars($_SESSION['user_email']); ?></span>
                                        <span class="role-badge-small"><?php echo ucfirst($_SESSION['user_role']); ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="user-menu-list">
                                <a href="profile.php" class="user-menu-item">
                                    My Profile
                                </a>
                                <a href="settings.php" class="user-menu-item">
                                    Account Settings
                                </a>
                                <a href="help.php" class="user-menu-item">
                                    Help & Support
                                </a>
                                <div class="menu-divider"></div>
                                <a href="logout.php" class="user-menu-item logout-item">
                                    Sign Out
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>
    
    <main class="main-content">
        <div class="welcome-section">
            <h2>Executive Dashboard</h2>
            <p>Welcome to your comprehensive customs clearing and import/export management portal. Access all your trade operations, documentation, and financial services through the modules below.</p>
        </div>
        
        <?php if (empty($available_modules)): ?>
            <div class="alert alert-info">
                <strong>No modules available.</strong> You don't have access to any modules at the moment.
                <div style="margin-top: 15px;">
                    <a href="request_access.php" class="btn btn-primary">
                        🔑 Request Module Access
                    </a>
                </div>
            </div>
        <?php else: ?>
            <div class="modules-grid">
                <?php foreach ($available_modules as $key => $module): ?>
                    <a href="<?php echo $module['url']; ?>" class="module-card" data-module="<?php echo $key; ?>">
                        <div class="module-header">
                            <div class="module-icon" style="--module-color: <?php echo $module['color']; ?>">
                                <?php
                                switch($module['icon']) {
                                    case 'invoices':
                                        echo '<svg width="32" height="32" viewBox="0 0 32 32" fill="currentColor">
                                                <path d="M6 4a2 2 0 00-2 2v20a2 2 0 002 2h20a2 2 0 002-2V6a2 2 0 00-2-2H6zm2 4h16v2H8V8zm0 4h16v2H8v-2zm0 4h12v2H8v-2zm0 4h10v2H8v-2z"/>
                                                <path d="M24 2l2 2-2 2-2-2z" opacity="0.7"/>
                                              </svg>';
                                        break;
                                    case 'accounting':
                                        echo '<svg width="32" height="32" viewBox="0 0 32 32" fill="currentColor">
                                                <path d="M16 2C8.268 2 2 8.268 2 16s6.268 14 14 14 14-6.268 14-14S23.732 2 16 2zm0 6a2 2 0 110 4 2 2 0 010-4zm-2 6h4v8h-4v-8z"/>
                                                <path d="M28 8l2 2v4l-2 2" opacity="0.6"/>
                                              </svg>';
                                        break;
                                    case 'freight':
                                        echo '<svg width="32" height="32" viewBox="0 0 32 32" fill="currentColor">
                                                <path d="M4 8a2 2 0 012-2h12a2 2 0 012 2v4h4l4 4v8a2 2 0 01-2 2h-2a4 4 0 11-8 0H12a4 4 0 11-8 0H2a2 2 0 01-2-2V8zm18 12a2 2 0 100 4 2 2 0 000-4zM8 20a2 2 0 100 4 2 2 0 000-4z"/>
                                                <path d="M2 4h4v2H2z M6 4h4v2H6z M10 4h4v2h-4z" opacity="0.5"/>
                                              </svg>';
                                        break;
                                    case 'tariff':
                                        echo '<svg width="32" height="32" viewBox="0 0 32 32" fill="currentColor">
                                                <path d="M4 4a2 2 0 00-2 2v20a2 2 0 002 2h24a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 4h20v4H6V8zm0 6h8v4H6v-4zm10 0h10v4H16v-4zm-10 6h8v4H6v-4zm10 0h10v4H16v-4z"/>
                                                <path d="M30 2v4h-4V2z" opacity="0.7"/>
                                              </svg>';
                                        break;
                                    case 'backoffice':
                                        echo '<svg width="32" height="32" viewBox="0 0 32 32" fill="currentColor">
                                                <path d="M4 6a2 2 0 012-2h20a2 2 0 012 2v20a2 2 0 01-2 2H6a2 2 0 01-2-2V6zm4 2v16h16V8H8zm4 4h8v2h-8v-2zm0 4h6v2h-6v-2z"/>
                                                <path d="M28 4h2v2h-2z M28 8h2v2h-2z" opacity="0.6"/>
                                              </svg>';
                                        break;
                                    default:
                                        echo '<svg width="32" height="32" viewBox="0 0 32 32" fill="currentColor">
                                                <path d="M16 2C8.268 2 2 8.268 2 16s6.268 14 14 14 14-6.268 14-14S23.732 2 16 2z"/>
                                              </svg>';
                                }
                                ?>
                            </div>
                            <div class="module-status">
                                <span class="status-indicator active"></span>
                                <span class="status-text">Active</span>
                            </div>
                        </div>

                        <div class="module-content">
                            <div class="module-title"><?php echo $module['title']; ?></div>
                            <div class="module-description"><?php echo $module['description']; ?></div>
                        </div>

                        <div class="module-footer">
                            <div class="module-actions">
                                <?php foreach ($module['actions'] as $index => $action): ?>
                                    <button class="action-btn"
                                            onclick="event.preventDefault(); event.stopPropagation(); handleModuleAction('<?php echo $key; ?>', '<?php echo $action['action']; ?>', '<?php echo $module['url']; ?>')"
                                            title="<?php echo $action['label']; ?>">
                                        <span class="action-icon"><?php echo $action['icon']; ?></span>
                                        <span class="action-label"><?php echo $action['label']; ?></span>
                                    </button>
                                <?php endforeach; ?>
                            </div>
                            <div class="module-enter">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                                    <path d="M8.636 3.5a.5.5 0 0 0-.5-.5H1.5A1.5 1.5 0 0 0 0 4.5v10A1.5 1.5 0 0 0 1.5 16h10a1.5 1.5 0 0 0 1.5-1.5V7.864a.5.5 0 0 0-.5-.5C11.864 7.364 8.636 4.136 8.636 3.5z"/>
                                    <path d="M16 .5a.5.5 0 0 0-.5-.5h-5a.5.5 0 0 0 0 1h3.793L6.146 9.146a.5.5 0 1 0 .708.708L15 1.707V5.5a.5.5 0 0 0 1 0v-5z"/>
                                </svg>
                            </div>
                        </div>
                    </a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
        
        <div class="dashboard-widgets">
            <div class="widgets-header">
                <h3>Trade Operations Center</h3>
                <div class="widgets-controls">
                    <button class="widget-toggle" data-widget="all" title="Toggle All Widgets">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                            <path d="M1 2.5A1.5 1.5 0 0 1 2.5 1h3A1.5 1.5 0 0 1 7 2.5v3A1.5 1.5 0 0 1 5.5 7h-3A1.5 1.5 0 0 1 1 5.5v-3zM2.5 2a.5.5 0 0 0-.5.5v3a.5.5 0 0 0 .5.5h3a.5.5 0 0 0 .5-.5v-3a.5.5 0 0 0-.5-.5h-3zm6.5.5A1.5 1.5 0 0 1 10.5 1h3A1.5 1.5 0 0 1 15 2.5v3A1.5 1.5 0 0 1 13.5 7h-3A1.5 1.5 0 0 1 9 5.5v-3zm1.5-.5a.5.5 0 0 0-.5.5v3a.5.5 0 0 0 .5.5h3a.5.5 0 0 0 .5-.5v-3a.5.5 0 0 0-.5-.5h-3zM1 10.5A1.5 1.5 0 0 1 2.5 9h3A1.5 1.5 0 0 1 7 10.5v3A1.5 1.5 0 0 1 5.5 15h-3A1.5 1.5 0 0 1 1 13.5v-3zm1.5-.5a.5.5 0 0 0-.5.5v3a.5.5 0 0 0 .5.5h3a.5.5 0 0 0 .5-.5v-3a.5.5 0 0 0-.5-.5h-3zm6.5.5A1.5 1.5 0 0 1 10.5 9h3a1.5 1.5 0 0 1 1.5 1.5v3a1.5 1.5 0 0 1-1.5 1.5h-3A1.5 1.5 0 0 1 9 13.5v-3zm1.5-.5a.5.5 0 0 0-.5.5v3a.5.5 0 0 0 .5.5h3a.5.5 0 0 0 .5-.5v-3a.5.5 0 0 0-.5-.5h-3z"/>
                        </svg>
                    </button>
                </div>
            </div>

            <div class="widgets-grid">
                <!-- Quick Payment Widget -->
                <div class="widget-card payment-widget" data-widget="payment">
                    <div class="widget-header">
                        <div class="widget-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M2 4a2 2 0 012-2h16a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2V4zm2 0v4h16V4H4zm0 8a2 2 0 012-2h16a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4zm2 0v4h16v-4H4z"/>
                            </svg>
                        </div>
                        <div class="widget-title">Customs Payments</div>
                        <button class="widget-expand" onclick="toggleWidget('payment')">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                                <path d="M8 4a.5.5 0 01.5.5v3h3a.5.5 0 010 1h-3v3a.5.5 0 01-1 0v-3h-3a.5.5 0 010-1h3v-3A.5.5 0 018 4z"/>
                            </svg>
                        </button>
                    </div>
                    <div class="widget-content">
                        <div class="payment-summary">
                            <div class="payment-amount">
                                <span class="amount-label">Outstanding Balance:</span>
                                <span class="amount-value">$<?php
                                    // Calculate outstanding balance from unpaid invoices
                                    $outstanding = 0;
                                    if (defined('FTP_DEMO_MODE') && FTP_DEMO_MODE) {
                                        $demo_files = getDemoFiles($_SESSION['user_email']);
                                        foreach ($demo_files as $file) {
                                            if (!checkDemoPaymentStatus($file['filename'], $_SESSION['user_email'])) {
                                                $outstanding += (100 + (crc32($file['filename']) % 500));
                                            }
                                        }
                                    }
                                    echo number_format($outstanding, 2);
                                ?></span>
                            </div>
                            <div class="payment-actions">
                                <button class="btn btn-primary btn-sm" onclick="window.location.href='modules/invoices.php'">
                                    💳 Pay Now
                                </button>
                                <button class="btn btn-secondary btn-sm" onclick="window.location.href='modules/invoices.php'">
                                    📄 View Invoices
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Document Center Widget -->
                <div class="widget-card document-widget" data-widget="documents">
                    <div class="widget-header">
                        <div class="widget-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M9 2a1 1 0 000 2h6a1 1 0 100-2H9z"/>
                                <path fill-rule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 001 1h8a1 1 0 001-1V3a2 2 0 012 2v6h-2V5H6v6H4V5z"/>
                                <path d="M3 19a2 2 0 002 2h1.5a.5.5 0 000-1H5a1 1 0 01-1-1v-1a.5.5 0 00-1 0v1z"/>
                                <path d="M9.5 21a.5.5 0 000-1H8a1 1 0 01-1-1v-1a.5.5 0 00-1 0v1a2 2 0 002 2h1.5z"/>
                                <path d="M15 21h1a2 2 0 002-2v-1a.5.5 0 00-1 0v1a1 1 0 01-1 1h-1a.5.5 0 000 1z"/>
                                <path d="M19 17.5a.5.5 0 00-1 0v1a1 1 0 01-1 1h-1a.5.5 0 000 1h1a2 2 0 002-2v-1z"/>
                            </svg>
                        </div>
                        <div class="widget-title">Trade Documents</div>
                        <button class="widget-expand" onclick="toggleWidget('documents')">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                                <path d="M8 4a.5.5 0 01.5.5v3h3a.5.5 0 010 1h-3v3a.5.5 0 01-1 0v-3h-3a.5.5 0 010-1h3v-3A.5.5 0 018 4z"/>
                            </svg>
                        </button>
                    </div>
                    <div class="widget-content">
                        <div class="document-list">
                            <div class="document-item" onclick="window.location.href='modules/invoices.php'">
                                <div class="doc-icon">📄</div>
                                <div class="doc-info">
                                    <div class="doc-name">Recent Invoices</div>
                                    <div class="doc-count"><?php
                                        // Count available invoices
                                        $invoice_count = 0;
                                        if (defined('FTP_DEMO_MODE') && FTP_DEMO_MODE) {
                                            $demo_files = getDemoFiles($_SESSION['user_email']);
                                            $invoice_count = count($demo_files);
                                        }
                                        echo $invoice_count;
                                    ?> documents</div>
                                </div>
                                <div class="doc-action">→</div>
                            </div>
                            <div class="document-item" onclick="showToast('Feature coming soon!', 'info')">
                                <div class="doc-icon">📊</div>
                                <div class="doc-info">
                                    <div class="doc-name">Reports</div>
                                    <div class="doc-count">3 available</div>
                                </div>
                                <div class="doc-action">→</div>
                            </div>
                            <div class="document-item" onclick="showToast('Feature coming soon!', 'info')">
                                <div class="doc-icon">📋</div>
                                <div class="doc-info">
                                    <div class="doc-name">Statements</div>
                                    <div class="doc-count">Monthly</div>
                                </div>
                                <div class="doc-action">→</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Status Widget -->
                <div class="widget-card status-widget" data-widget="status">
                    <div class="widget-header">
                        <div class="widget-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                        </div>
                        <div class="widget-title">Customs Systems</div>
                        <button class="widget-expand" onclick="toggleWidget('status')">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                                <path d="M8 4a.5.5 0 01.5.5v3h3a.5.5 0 010 1h-3v3a.5.5 0 01-1 0v-3h-3a.5.5 0 010-1h3v-3A.5.5 0 018 4z"/>
                            </svg>
                        </button>
                    </div>
                    <div class="widget-content">
                        <div class="status-list">
                            <div class="status-item">
                                <div class="status-indicator online"></div>
                                <div class="status-info">
                                    <div class="status-name">Portal Services</div>
                                    <div class="status-desc">All systems operational</div>
                                </div>
                                <div class="status-time">99.9% uptime</div>
                            </div>
                            <div class="status-item">
                                <div class="status-indicator warning"></div>
                                <div class="status-info">
                                    <div class="status-name">FTP Server</div>
                                    <div class="status-desc">Demo mode active</div>
                                </div>
                                <div class="status-time">Maintenance</div>
                            </div>
                            <div class="status-item">
                                <div class="status-indicator online"></div>
                                <div class="status-info">
                                    <div class="status-name">Payment Gateway</div>
                                    <div class="status-desc">Processing normally</div>
                                </div>
                                <div class="status-time">Active</div>
                            </div>
                        </div>
                        <div class="status-actions">
                            <button class="btn btn-sm btn-secondary" onclick="showToast('Status refreshed!', 'success')">
                                🔄 Refresh Status
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity Widget -->
                <div class="widget-card activity-widget" data-widget="activity">
                    <div class="widget-header">
                        <div class="widget-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                            </svg>
                        </div>
                        <div class="widget-title">Trade Activity</div>
                        <button class="widget-expand" onclick="toggleWidget('activity')">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                                <path d="M8 4a.5.5 0 01.5.5v3h3a.5.5 0 010 1h-3v3a.5.5 0 01-1 0v-3h-3a.5.5 0 010-1h3v-3A.5.5 0 018 4z"/>
                            </svg>
                        </button>
                    </div>
                    <div class="widget-content">
                        <div class="activity-list">
                            <div class="activity-item">
                                <div class="activity-icon">💳</div>
                                <div class="activity-info">
                                    <div class="activity-desc">Payment processed for Invoice #INV-2024-001</div>
                                    <div class="activity-time">2 hours ago</div>
                                </div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-icon">📄</div>
                                <div class="activity-info">
                                    <div class="activity-desc">New invoice uploaded to your account</div>
                                    <div class="activity-time">1 day ago</div>
                                </div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-icon">🔐</div>
                                <div class="activity-info">
                                    <div class="activity-desc">Successful login from your location</div>
                                    <div class="activity-time">2 days ago</div>
                                </div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-icon">📊</div>
                                <div class="activity-info">
                                    <div class="activity-desc">Monthly statement generated</div>
                                    <div class="activity-time">1 week ago</div>
                                </div>
                            </div>
                        </div>
                        <div class="activity-actions">
                            <button class="btn btn-sm btn-secondary" onclick="showToast('Feature coming soon!', 'info')">
                                📋 View All Activity
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay">
        <div class="loading-spinner">
            <div class="spinner-ring"></div>
            <div class="loading-text">Loading Dashboard...</div>
        </div>
    </div>

    <script src="assets/js/script.js"></script>
    <script>
        // Interactive Header Functions
        function toggleNotifications() {
            const dropdown = document.getElementById('notificationsDropdown');
            const settingsDropdown = document.getElementById('settingsDropdown');
            const modulesDropdown = document.getElementById('modulesDropdown');

            // Close other dropdowns if open
            settingsDropdown.classList.remove('show');
            modulesDropdown.classList.remove('show');

            // Toggle notifications dropdown
            dropdown.classList.toggle('show');
        }

        function toggleSettings() {
            const dropdown = document.getElementById('settingsDropdown');
            const notificationsDropdown = document.getElementById('notificationsDropdown');
            const modulesDropdown = document.getElementById('modulesDropdown');

            // Close other dropdowns if open
            notificationsDropdown.classList.remove('show');
            modulesDropdown.classList.remove('show');

            // Toggle settings dropdown (gear icon)
            dropdown.classList.toggle('show');
        }

        function toggleModulesDropdown() {
            const dropdown = document.getElementById('modulesDropdown');
            const notificationsDropdown = document.getElementById('notificationsDropdown');
            const settingsDropdown = document.getElementById('settingsDropdown');

            // Close other dropdowns if open
            notificationsDropdown.classList.remove('show');
            settingsDropdown.classList.remove('show');

            // Toggle modules dropdown
            dropdown.classList.toggle('show');
        }

        function markAllRead() {
            const badge = document.querySelector('.notification-badge');
            const notifications = document.querySelectorAll('.notification-item.unread');

            // Remove unread class from all notifications
            notifications.forEach(notification => {
                notification.classList.remove('unread');
            });

            // Update badge
            badge.textContent = '0';
            badge.style.display = 'none';

            // Show success message
            showToast('All notifications marked as read', 'success');
        }

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(event) {
            const notificationContainer = document.querySelector('.notification-container');
            const userMenuContainer = document.querySelector('.user-menu-container');
            const modulesContainer = document.querySelector('.modules-dropdown-container');
            const notificationsDropdown = document.getElementById('notificationsDropdown');
            const settingsDropdown = document.getElementById('settingsDropdown');
            const modulesDropdown = document.getElementById('modulesDropdown');

            if (notificationContainer && !notificationContainer.contains(event.target)) {
                notificationsDropdown.classList.remove('show');
            }

            if (userMenuContainer && !userMenuContainer.contains(event.target)) {
                settingsDropdown.classList.remove('show');
            }

            if (modulesContainer && !modulesContainer.contains(event.target)) {
                modulesDropdown.classList.remove('show');
            }
        });

        // Show toast notification
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.textContent = message;

            // Add toast to page
            document.body.appendChild(toast);

            // Show toast
            setTimeout(() => toast.classList.add('show'), 100);

            // Remove toast after 3 seconds
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => document.body.removeChild(toast), 300);
            }, 3000);
        }

        // Widget Interactions
        function toggleWidget(widgetType) {
            const widget = document.querySelector(`[data-widget="${widgetType}"]`);
            const content = widget.querySelector('.widget-content');
            const expandBtn = widget.querySelector('.widget-expand');

            if (content.style.display === 'none') {
                content.style.display = 'block';
                expandBtn.innerHTML = '<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor"><path d="M8 4a.5.5 0 01.5.5v3h3a.5.5 0 010 1h-3v3a.5.5 0 01-1 0v-3h-3a.5.5 0 010-1h3v-3A.5.5 0 018 4z"/></svg>';
                widget.classList.remove('collapsed');
            } else {
                content.style.display = 'none';
                expandBtn.innerHTML = '<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor"><path d="M4 8a.5.5 0 01.5-.5h7a.5.5 0 010 1h-7A.5.5 0 014 8z"/></svg>';
                widget.classList.add('collapsed');
            }
        }

        // Initialize widgets
        document.addEventListener('DOMContentLoaded', function() {
            // Add hover effects to widgets
            const widgets = document.querySelectorAll('.widget-card');
            widgets.forEach(widget => {
                widget.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });

                widget.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // Add click effects to document items
            const docItems = document.querySelectorAll('.document-item');
            docItems.forEach(item => {
                item.addEventListener('click', function() {
                    this.style.transform = 'scale(0.98)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });

            // Add click effects to activity items
            const activityItems = document.querySelectorAll('.activity-item');
            activityItems.forEach(item => {
                item.addEventListener('click', function() {
                    showToast('Activity details coming soon!', 'info');
                });
            });
        });

        // Module Action Handler
        function handleModuleAction(moduleKey, action, moduleUrl) {
            console.log('Module Action:', moduleKey, action, moduleUrl);

            switch(action) {
                // Invoice Actions
                case 'viewInvoices':
                    window.location.href = moduleUrl;
                    break;
                case 'payOutstanding':
                    showToast('Redirecting to payment portal...', 'info');
                    setTimeout(() => {
                        window.location.href = moduleUrl + '?action=pay';
                    }, 1000);
                    break;
                case 'downloadPDFs':
                    showToast('Starting bulk PDF download...', 'info');
                    setTimeout(() => {
                        window.location.href = moduleUrl + '?action=download_all';
                    }, 1000);
                    break;

                // Freight Actions
                case 'trackShipments':
                    showToast('Opening shipment tracking...', 'info');
                    setTimeout(() => {
                        window.location.href = moduleUrl + '?action=track';
                    }, 1000);
                    break;
                case 'newBooking':
                    showToast('Opening booking form...', 'info');
                    setTimeout(() => {
                        window.location.href = moduleUrl + '?action=book';
                    }, 1000);
                    break;
                case 'viewReports':
                    showToast('Loading freight reports...', 'info');
                    setTimeout(() => {
                        window.location.href = moduleUrl + '?action=reports';
                    }, 1000);
                    break;

                // Tariff Actions
                case 'calculateDuty':
                    showToast('Opening duty calculator...', 'info');
                    setTimeout(() => {
                        window.location.href = moduleUrl + '?action=calculate';
                    }, 1000);
                    break;
                case 'searchCodes':
                    showToast('Opening tariff code search...', 'info');
                    setTimeout(() => {
                        window.location.href = moduleUrl + '?action=search';
                    }, 1000);
                    break;
                case 'rateLookup':
                    showToast('Opening rate lookup tool...', 'info');
                    setTimeout(() => {
                        window.location.href = moduleUrl + '?action=lookup';
                    }, 1000);
                    break;

                // Accounting Actions
                case 'viewStatements':
                    showToast('Loading account statements...', 'info');
                    setTimeout(() => {
                        window.location.href = moduleUrl + '?action=statements';
                    }, 1000);
                    break;
                case 'generateReport':
                    showToast('Generating financial report...', 'info');
                    setTimeout(() => {
                        window.location.href = moduleUrl + '?action=report';
                    }, 1000);
                    break;
                case 'exportData':
                    showToast('Preparing data export...', 'info');
                    setTimeout(() => {
                        window.location.href = moduleUrl + '?action=export';
                    }, 1000);
                    break;

                // Back Office Actions
                case 'manageDocs':
                    showToast('Opening document manager...', 'info');
                    setTimeout(() => {
                        window.location.href = moduleUrl + '?action=docs';
                    }, 1000);
                    break;
                case 'systemConfig':
                    showToast('Opening system configuration...', 'info');
                    setTimeout(() => {
                        window.location.href = moduleUrl + '?action=config';
                    }, 1000);
                    break;
                case 'userAccess':
                    showToast('Opening user access management...', 'info');
                    setTimeout(() => {
                        window.location.href = moduleUrl + '?action=users';
                    }, 1000);
                    break;

                default:
                    showToast('Feature coming soon!', 'info');
                    setTimeout(() => {
                        window.location.href = moduleUrl;
                    }, 1000);
            }
        }

        // Enhanced Dashboard Interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Hide loading overlay
            const loadingOverlay = document.getElementById('loading-overlay');
            setTimeout(() => {
                loadingOverlay.style.opacity = '0';
                setTimeout(() => {
                    loadingOverlay.style.display = 'none';
                }, 300);
            }, 800);

            // Simple module card interactions (no animations)
            const moduleCards = document.querySelectorAll('.module-card');
            moduleCards.forEach(card => {
                // Just ensure cards are clickable - no special effects
                card.style.cursor = 'pointer';
            });

            // Animate progress bars
            const progressBars = document.querySelectorAll('.progress-bar');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const bar = entry.target;
                        const width = bar.style.width;
                        bar.style.width = '0%';
                        setTimeout(() => {
                            bar.style.width = width;
                        }, 100);
                    }
                });
            });

            progressBars.forEach(bar => observer.observe(bar));

            // Add refresh functionality
            const refreshBtn = document.querySelector('.refresh-btn');
            if (refreshBtn) {
                refreshBtn.addEventListener('click', function() {
                    this.style.transform = 'rotate(360deg)';
                    setTimeout(() => {
                        this.style.transform = 'rotate(0deg)';
                    }, 500);
                });
            }
        });
    </script>
</body>
</html>


